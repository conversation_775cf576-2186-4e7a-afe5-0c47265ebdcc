import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Globe, Shield, Users } from 'lucide-react';
import { toast } from 'sonner';
import { MessageList } from './MessageList';
import { ChatInput } from './ChatInput';
import { useChatMessages } from './hooks/useChatMessages';
import { useChatSubscription } from './hooks/useChatSubscription';
import { fetchGlobalMessages, fetchGuildMessages } from './api';
import { ChatTab, GuildMembership } from './types';

export function ChatPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<ChatTab>('global');
  
  const {
    globalMessages,
    guildMessages,
    setGlobalMessages,
    setGuildMessages,
    addOptimisticMessage,
    addGlobalMessage,
    addGuildMessage,
  } = useChatMessages();

  // Fetch user's guild membership
  const { data: guildMembership } = useQuery({
    queryKey: ['guild-membership', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;
      
      const { data, error } = await supabase
        .from('guild_members')
        .select(`
          guild_id,
          guilds (
            name,
            icon_name
          )
        `)
        .eq('user_id', user.id)
        .eq('status', 'accepted')
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching guild membership:', error);
        return null;
      }
      
      return data as GuildMembership;
    },
    enabled: !!user?.id,
  });

  // Fetch global messages
  const { data: initialGlobalMessages, isLoading: globalLoading } = useQuery({
    queryKey: ['global-messages'],
    queryFn: fetchGlobalMessages,
    enabled: activeTab === 'global',
  });

  // Fetch guild messages
  const { data: initialGuildMessages, isLoading: guildLoading } = useQuery({
    queryKey: ['guild-messages', guildMembership?.guild_id],
    queryFn: () => fetchGuildMessages(guildMembership!.guild_id),
    enabled: activeTab === 'guild' && !!guildMembership?.guild_id,
  });

  // Set initial messages when data loads
  useEffect(() => {
    if (initialGlobalMessages && activeTab === 'global') {
      setGlobalMessages(initialGlobalMessages);
    }
  }, [initialGlobalMessages, activeTab, setGlobalMessages]);

  useEffect(() => {
    if (initialGuildMessages && activeTab === 'guild') {
      setGuildMessages(initialGuildMessages);
    }
  }, [initialGuildMessages, activeTab, setGuildMessages]);

  // Set up real-time subscriptions
  useChatSubscription({
    activeTab,
    guildId: guildMembership?.guild_id,
    onGlobalMessage: addGlobalMessage,
    onGuildMessage: addGuildMessage,
  });

  const handleOptimisticMessage = (content: string) => {
    if (!user?.id) return;
    addOptimisticMessage(content, user.id, activeTab, guildMembership?.guild_id);
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-white/70">Please log in to access chat.</p>
      </div>
    );
  }

  const currentMessages = activeTab === 'global' ? globalMessages : guildMessages;
  const isLoading = activeTab === 'global' ? globalLoading : guildLoading;

  return (
    <div className="flex flex-col h-full">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ChatTab)} className="flex flex-col h-full">
        <div className="p-4 border-b border-white/10">
          <TabsList className="grid w-full grid-cols-2 bg-white/5">
            <TabsTrigger value="global" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Global Chat
            </TabsTrigger>
            <TabsTrigger value="guild" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Guild Chat
              {guildMembership && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {guildMembership.guilds?.name}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="global" className="flex-1 flex flex-col m-0 min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center flex-1">
              <p className="text-white/70">Loading messages...</p>
            </div>
          ) : (
            <>
              <MessageList
                messages={currentMessages}
                currentUserId={user.id}
                isGuildChat={false}
              />
              <ChatInput
                currentUserId={user.id}
                activeTab={activeTab}
                onOptimisticMessage={handleOptimisticMessage}
              />
            </>
          )}
        </TabsContent>

        <TabsContent value="guild" className="flex-1 flex flex-col m-0 min-h-0">
          {!guildMembership ? (
            <div className="flex items-center justify-center flex-1 flex-col gap-4">
              <Users className="h-12 w-12 text-white/30" />
              <div className="text-center">
                <p className="text-white/70 mb-2">You're not in a guild yet</p>
                <p className="text-white/50 text-sm">Join a guild to access guild chat</p>
              </div>
            </div>
          ) : isLoading ? (
            <div className="flex items-center justify-center flex-1">
              <p className="text-white/70">Loading guild messages...</p>
            </div>
          ) : (
            <>
              <MessageList
                messages={currentMessages}
                currentUserId={user.id}
                isGuildChat={true}
              />
              <ChatInput
                currentUserId={user.id}
                activeTab={activeTab}
                guildId={guildMembership.guild_id}
                onOptimisticMessage={handleOptimisticMessage}
              />
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
